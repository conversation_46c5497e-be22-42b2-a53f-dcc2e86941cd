"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Crown, Diamond, Star, ArrowRight, CheckCircle, Target, TrendingUp, DollarSign } from "lucide-react"

export default function HowItWorksSection() {
  const steps = [
    {
      number: "01",
      title: "Choose Your Elite Challenge",
      description: "Select from our premium range of account sizes and challenge parameters tailored for professional traders.",
      icon: <Target className="h-8 w-8 text-sky-400" />,
      color: "from-sky-500/20 to-blue-500/20",
    },
    {
      number: "02",
      title: "Master the Challenge",
      description: "Demonstrate your exceptional trading skills by meeting profit targets while maintaining strict risk management.",
      icon: <TrendingUp className="h-8 w-8 text-sky-400" />,
      color: "from-blue-500/20 to-sky-500/20",
    },
    {
      number: "03",
      title: "Receive Premium Funding",
      description: "Upon successful completion, gain access to substantial capital with our exclusive funding program.",
      icon: <CheckCircle className="h-8 w-8 text-sky-400" />,
      color: "from-sky-500/20 to-blue-500/20",
    },
    {
      number: "04",
      title: "Generate Elite Profits",
      description: "Trade with our capital and retain up to 90% of profits with our industry-leading profit sharing model.",
      icon: <DollarSign className="h-8 w-8 text-sky-400" />,
      color: "from-blue-500/20 to-sky-500/20",
    },
  ]

  return (
    <section className="py-28 bg-gradient-to-b from-[#00253c] via-[#001e30] to-[#001a2c] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-72 h-72 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 right-20 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-sky-400/15 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-30, -120],
              opacity: [0, 0.6, 0],
              scale: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 8,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm">Our Process</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            How{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              It Works
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Simple four-step process to get you trading with our capital
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.15 }}
              whileHover={{ y: -10, scale: 1.03 }}
              className="group relative"
            >
              {/* Connection line for desktop */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-sky-400/50 to-blue-400/50 z-20">
                  <ArrowRight className="h-4 w-4 text-sky-400 absolute -top-2 right-0" />
                </div>
              )}

              <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative">
                {/* Step number */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-sky-500/20 rounded-full flex items-center justify-center border border-sky-400/30">
                  <span className="text-sm font-bold text-sky-400">{step.number}</span>
                </div>

                <CardContent className="pt-6 pb-6 px-6">
                  {/* Icon container */}
                  <div className="mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-sky-500/20 to-blue-500/20 rounded-lg flex items-center justify-center border border-sky-400/30">
                      {step.icon}
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold mb-3 text-white">
                    {step.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed text-sm">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center"
        >
          <div className="inline-flex items-center bg-sky-500/10 border border-sky-400/20 rounded-full px-6 py-3">
            <span className="text-sky-400 font-medium">Ready to Start Trading?</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
