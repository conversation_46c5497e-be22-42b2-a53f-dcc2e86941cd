"use client"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, HelpCircle, Star, Crown } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function PricingSection() {
  const accountTypes = [
    {
      id: "step1",
      name: "Step 1",
      description: "The first phase of our two-step evaluation process",
      plans: [
        {
          capital: "$1,000",
          price: "$49",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$3,000",
          price: "$99",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$5,000",
          price: "$149",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$10,000",
          price: "$199",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$25,000",
          price: "$349",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$50,000",
          price: "$499",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$100,000",
          price: "$799",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$200,000",
          price: "$1,299",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$500,000",
          price: "$2,499",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
      ],
    },
    {
      id: "step2",
      name: "Step 2",
      description: "The second phase of our two-step evaluation process",
      plans: [
        {
          capital: "$1,000",
          price: "$39",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$3,000",
          price: "$79",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$5,000",
          price: "$119",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$10,000",
          price: "$159",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$25,000",
          price: "$279",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$50,000",
          price: "$399",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$100,000",
          price: "$639",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$200,000",
          price: "$1,039",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$500,000",
          price: "$1,999",
          profitTarget: "10%",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "No time limit",
            "Weekend holding allowed",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
      ],
    },
    {
      id: "hft-neo",
      name: "HFT Neo",
      description: "Optimized for high-frequency traders with lower latency",
      plans: [
        {
          capital: "$1,000",
          price: "$69",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$3,000",
          price: "$149",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$5,000",
          price: "$219",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$10,000",
          price: "$299",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$25,000",
          price: "$499",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$50,000",
          price: "$649",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$100,000",
          price: "$999",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$200,000",
          price: "$1,599",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
        {
          capital: "$500,000",
          price: "$2,999",
          profitTarget: "10%",
          maxDrawdown: "3%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Optimized for scalping",
            "Low latency execution",
            "Advanced trading tools",
            "Scaling opportunities",
            "24/7 Support",
          ],
        },
      ],
    },
    {
      id: "instant",
      name: "Instant",
      description: "Skip the evaluation and get funded immediately",
      plans: [
        {
          capital: "$1,000",
          price: "$199",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$3,000",
          price: "$399",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$5,000",
          price: "$599",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$10,000",
          price: "$799",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$25,000",
          price: "$1,299",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$50,000",
          price: "$1,899",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$100,000",
          price: "$2,999",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$200,000",
          price: "$4,999",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
        {
          capital: "$500,000",
          price: "$9,999",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "70%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Bi-weekly payouts",
            "24/7 Support",
          ],
        },
      ],
    },
  ]

  return (
    <section id="pricing" className="py-24 relative bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#00253c]">
      <div className="absolute inset-0 bg-gradient-radial from-sky-500/5 via-transparent to-transparent" />

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -80],
              opacity: [0, 0.6, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 6 + Math.random() * 3,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 6,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm">Funding Plans</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Professional Pricing Plans
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Choose the perfect funding plan for your trading style and goals. All plans include professional features and support.
          </p>
        </motion.div>

        <Tabs defaultValue="step1" className="w-full">
          <div className="flex justify-center mb-12 overflow-x-auto pb-2">
            <TabsList className="bg-gradient-to-r from-[#002a3c] to-[#001e30] border border-sky-500/20">
              {accountTypes.map((type) => (
                <TabsTrigger
                  key={type.id}
                  value={type.id}
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sky-500 data-[state=active]:to-blue-500 data-[state=active]:text-white px-6 py-3 transition-all duration-300"
                >
                  {type.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {accountTypes.map((accountType) => (
            <TabsContent key={accountType.id} value={accountType.id}>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{accountType.name}</h3>
                <p className="text-gray-400">{accountType.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6">
                {accountType.plans.slice(0, 8).map((plan, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)" }}
                    className="transition-all duration-300 ease-in-out"
                  >
                    <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative group">
                      {/* Popular badge for certain plans */}
                      {(plan.capital === "$10,000" || plan.capital === "$25,000") && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <div className="bg-gradient-to-r from-sky-500 to-blue-500 text-white text-xs font-semibold px-3 py-1 rounded-full">
                            POPULAR
                          </div>
                        </div>
                      )}

                      <CardHeader>
                        <CardTitle className="text-white text-lg">{accountType.name}</CardTitle>
                        <CardDescription className="text-gray-400">Trading Challenge</CardDescription>
                        <div className="mt-4">
                          <div className="text-2xl font-bold text-white">{plan.capital}</div>
                          <div className="text-sky-400 font-semibold text-lg">{plan.price}</div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                            <div className="flex items-center">
                              <span className="text-gray-400">Profit Target</span>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-56">The profit target you need to reach to pass the evaluation.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="font-medium text-white">{plan.profitTarget}</span>
                          </div>
                          <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                            <div className="flex items-center">
                              <span className="text-gray-400">Max Drawdown</span>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-56">The maximum allowed loss from your initial balance.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="font-medium text-white">{plan.maxDrawdown}</span>
                          </div>
                          <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                            <div className="flex items-center">
                              <span className="text-gray-400">Profit Split</span>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="w-56">Your share of the profits after passing the evaluation.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <span className="font-medium text-white">{plan.profitSplit}</span>
                          </div>
                        </div>

                        <ul className="space-y-3 mt-6">
                          {plan.features.map((feature, idx) => (
                            <li key={idx} className="flex items-start">
                              <Check className="h-5 w-5 text-sky-400 mr-2 shrink-0" />
                              <span className="text-gray-300 text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full bg-gradient-to-r from-sky-500 to-blue-500 text-white hover:from-sky-600 hover:to-blue-600 transition-all duration-300">
                          Get Started
                        </Button>
                      </CardFooter>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* $500,000 plan centered below if it exists */}
              {accountType.plans.length > 8 && (
                <div className="flex justify-center mt-6">
                  <div className="w-full max-w-sm">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.8 }}
                      whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)" }}
                      className="transition-all duration-300 ease-in-out"
                    >
                      <Card className="bg-[#002a3c] border-[#003a4c] h-full hover:brightness-110 hover:border-sky-500 hover:shadow-xl">
                        <CardHeader>
                          <CardTitle className="text-white">{accountType.name}</CardTitle>
                          <CardDescription className="text-gray-400">Trading Challenge</CardDescription>
                          <div className="mt-4">
                            <div className="text-3xl font-bold text-white">{accountType.plans[8].capital}</div>
                            <div className="text-sky-400 font-medium">{accountType.plans[8].price}</div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                              <div className="flex items-center">
                                <span className="text-gray-400">Profit Target</span>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p className="w-56">
                                        The profit target you need to reach to pass the evaluation.
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                              <span className="font-medium text-white">{accountType.plans[8].profitTarget}</span>
                            </div>
                            <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                              <div className="flex items-center">
                                <span className="text-gray-400">Max Drawdown</span>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p className="w-56">The maximum allowed loss from your initial balance.</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                              <span className="font-medium text-white">{accountType.plans[8].maxDrawdown}</span>
                            </div>
                            <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                              <div className="flex items-center">
                                <span className="text-gray-400">Profit Split</span>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <HelpCircle className="h-4 w-4 text-gray-600 ml-1" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p className="w-56">Your share of the profits after passing the evaluation.</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                              <span className="font-medium text-white">{accountType.plans[8].profitSplit}</span>
                            </div>
                          </div>

                          <ul className="space-y-3 mt-6">
                            {accountType.plans[8].features.map((feature, idx) => (
                              <li key={idx} className="flex items-start">
                                <Check className="h-5 w-5 text-sky-400 mr-2 shrink-0" />
                                <span className="text-gray-300 text-sm">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                        <CardFooter>
                          <Button className="w-full bg-[#003a4c] text-white hover:bg-[#004a5c]">Get Started</Button>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  </div>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  )
}
