"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Crown, Diamond, Quote, Award, TrendingUp } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Elite Professional Trader",
      content:
        "After trying several funding programs, Funded Whales stands out with their transparent rules and exceptional support. I passed the evaluation in just 2 weeks and have been consistently profitable since. The platform's sophistication is unmatched.",
      rating: 5,
      location: "London, UK",
      profit: "$127,500",
      badge: "Elite",
    },
    {
      name: "<PERSON>",
      role: "High-Frequency Day Trader",
      content:
        "The HFT Neo program is perfect for my scalping strategy. Ultra-low latency execution and the 90% profit split make this the best funding option I've found. Absolutely revolutionary platform!",
      rating: 5,
      location: "Sydney, Australia",
      profit: "$89,200",
      badge: "Premium",
    },
    {
      name: "<PERSON>",
      role: "Institutional Swing Trader",
      content:
        "What impressed me most was how quickly I received my first payout. The process was seamless, and the funds were in my account within 24 hours. Their educational resources and analytics are world-class.",
      rating: 5,
      location: "Toronto, Canada",
      profit: "$156,800",
      badge: "Elite",
    },
    {
      name: "Liam D.",
      role: "Quantitative Day Trader",
      content:
        "I wasn't sure what to expect, but the evaluation was smooth and fair. I passed in 12 days, and the funding came through without any issues. The platform's technology is cutting-edge.",
      rating: 5,
      location: "Manchester, UK",
      profit: "$73,400",
      badge: "Premium",
    },
    {
      name: "Priya M.",
      role: "Algorithmic Futures Trader",
      content:
        "What stood out was the execution speed and how clear the rules were. No hidden fees or surprises. Just solid support and a platform that delivers on every promise. Exceptional experience.",
      rating: 5,
      location: "Bangalore, India",
      profit: "$94,600",
      badge: "Elite",
    },
    {
      name: "Carlos R.",
      role: "Professional Forex Trader",
      content:
        "The payout process was straightforward and lightning-fast. I hit my first target and got paid in under 24 hours. No drama — just exactly what they promised. Premium service all the way.",
      rating: 5,
      location: "Mexico City, Mexico",
      profit: "$112,300",
      badge: "Premium",
    },
  ]

  return (
    <section className="py-28 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-25, -100],
              opacity: [0, 0.7, 0],
              scale: [0.4, 1.1, 0.4],
            }}
            transition={{
              duration: 7 + Math.random() * 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 7,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm">Success Stories</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Trader{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Success Stories
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Real testimonials from traders who have achieved success with our platform
          </p>
        </motion.div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 6000,
            }),
          ]}
          className="w-full max-w-6xl mx-auto"
        >
          <CarouselContent className="-ml-4">
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="pl-4 md:basis-1/2 lg:basis-1/3">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group h-full"
                >
                  <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative">
                    <CardContent className="p-6">
                      {/* Rating stars */}
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                        ))}
                      </div>

                      {/* Testimonial content */}
                      <p className="text-gray-300 mb-4 italic leading-relaxed">
                        "{testimonial.content}"
                      </p>

                      {/* Trader info */}
                      <div className="border-t border-sky-500/20 pt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-semibold text-white">
                              {testimonial.name}
                            </p>
                            <p className="text-sm text-sky-400">{testimonial.role}</p>
                            <p className="text-xs text-gray-400">{testimonial.location}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-sky-400 font-semibold">{testimonial.profit}</p>
                            <p className="text-xs text-gray-400">Profits</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
          <CarouselNext className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
        </Carousel>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-12"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="bg-sky-500/10 border border-sky-400/20 rounded-lg px-6 py-4">
              <div className="text-2xl font-bold text-sky-400 mb-1">$2.4M+</div>
              <div className="text-gray-300 text-sm">Total Profits Paid</div>
            </div>
            <div className="bg-sky-500/10 border border-sky-400/20 rounded-lg px-6 py-4">
              <div className="text-2xl font-bold text-sky-400 mb-1">10,000+</div>
              <div className="text-gray-300 text-sm">Active Traders</div>
            </div>
            <div className="bg-sky-500/10 border border-sky-400/20 rounded-lg px-6 py-4">
              <div className="text-2xl font-bold text-sky-400 mb-1">98%</div>
              <div className="text-gray-300 text-sm">Success Rate</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
